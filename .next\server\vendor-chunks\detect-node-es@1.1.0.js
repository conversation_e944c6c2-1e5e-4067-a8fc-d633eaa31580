/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/detect-node-es@1.1.0";
exports.ids = ["vendor-chunks/detect-node-es@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/detect-node-es@1.1.0/node_modules/detect-node-es/es5/node.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/detect-node-es@1.1.0/node_modules/detect-node-es/es5/node.js ***!
  \*****************************************************************************************/
/***/ ((module) => {

eval("// Only Node.JS has a process variable that is of [[Class]] process\nmodule.exports.isNode = Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZGV0ZWN0LW5vZGUtZXNAMS4xLjAvbm9kZV9tb2R1bGVzL2RldGVjdC1ub2RlLWVzL2VzNS9ub2RlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EscUJBQXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbnVxcy1kZW1vLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2RldGVjdC1ub2RlLWVzQDEuMS4wL25vZGVfbW9kdWxlcy9kZXRlY3Qtbm9kZS1lcy9lczUvbm9kZS5qcz8zNDJhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE9ubHkgTm9kZS5KUyBoYXMgYSBwcm9jZXNzIHZhcmlhYmxlIHRoYXQgaXMgb2YgW1tDbGFzc11dIHByb2Nlc3Ncbm1vZHVsZS5leHBvcnRzLmlzTm9kZSA9IE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbCh0eXBlb2YgcHJvY2VzcyAhPT0gJ3VuZGVmaW5lZCcgPyBwcm9jZXNzIDogMCkgPT09ICdbb2JqZWN0IHByb2Nlc3NdJztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/detect-node-es@1.1.0/node_modules/detect-node-es/es5/node.js\n");

/***/ })

};
;