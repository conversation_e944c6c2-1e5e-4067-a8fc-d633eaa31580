'use client';

import { useQueryState } from 'nuqs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface FilterState {
  category: string;
  priceRange: string;
  sortBy: string;
  inStock: boolean;
}

export function DemoAdvanced() {
  // Complex state with custom parser/serializer
  const [filters, setFilters] = useQueryState('filters', {
    defaultValue: {
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    },
    parse: value => {
      try {
        return JSON.parse(value) as FilterState;
      } catch {
        return {
          category: 'all',
          priceRange: '0-100',
          sortBy: 'name',
          inStock: true,
        };
      }
    },
    serialize: value => JSON.stringify(value),
  });

  // Individual query states with validation
  const [searchTerm, setSearchTerm] = useQueryState('search', {
    validate: value => value === null || value.length >= 2 || 'Search term must be at least 2 characters',
  });

  const [page, setPage] = useQueryState('page', {
    defaultValue: 1,
    parse: value => {
      const parsed = parseInt(value);
      return isNaN(parsed) || parsed < 1 ? 1 : parsed;
    },
  });

  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  const resetFilters = () => {
    setFilters({
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Nuqs Features</CardTitle>
          <CardDescription>Complex state management with custom parsers, validation, and type safety</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search with validation */}
          <div>
            <label className="text-sm font-medium">Search Products:</label>
            <Input
              value={searchTerm || ''}
              onChange={e => setSearchTerm(e.target.value || null)}
              placeholder="Enter search term (min 2 chars)..."
              className="mt-1"
            />
            {searchTerm && searchTerm.length < 2 && <p className="text-sm text-red-500 mt-1">Search term must be at least 2 characters</p>}
          </div>

          {/* Complex filters object */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Category:</label>
              <Select value={filters?.category} onValueChange={value => updateFilter('category', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="clothing">Clothing</SelectItem>
                  <SelectItem value="books">Books</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Price Range:</label>
              <Select value={filters?.priceRange} onValueChange={value => updateFilter('priceRange', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-100">$0 - $100</SelectItem>
                  <SelectItem value="100-500">$100 - $500</SelectItem>
                  <SelectItem value="500-1000">$500 - $1000</SelectItem>
                  <SelectItem value="1000+">$1000+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Sort By:</label>
              <Select value={filters?.sortBy} onValueChange={value => updateFilter('sortBy', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="date">Date Added</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">In Stock Only:</label>
              <div className="mt-1">
                <Button variant={filters?.inStock ? 'default' : 'outline'} size="sm" onClick={() => updateFilter('inStock', !filters?.inStock)}>
                  {filters?.inStock ? 'Yes' : 'No'}
                </Button>
              </div>
            </div>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setPage(Math.max(1, (page || 1) - 1))} disabled={(page || 1) <= 1}>
                Previous
              </Button>
              <span className="text-sm">Page {page || 1}</span>
              <Button variant="outline" size="sm" onClick={() => setPage((page || 1) + 1)}>
                Next
              </Button>
            </div>
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Reset Filters
            </Button>
          </div>

          {/* Current state display */}
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Current State:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(
                {
                  search: searchTerm,
                  page: page,
                  filters: filters,
                },
                null,
                2
              )}
            </pre>
          </div>

          {/* URL display */}
          <div className="text-xs text-muted-foreground">
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.search : ''}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
