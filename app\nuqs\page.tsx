'use client';

import { parseAsInteger, useQueryState } from 'nuqs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Link from 'next/link';

interface FilterState {
  category: string;
  priceRange: string;
  sortBy: string;
  inStock: boolean;
}

export default function NuqsPage() {
  // Clean, type-safe query state management with nuqs
  const [searchTerm, setSearchTerm] = useQueryState('search');
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1));
  const [sortOrder, setSortOrder] = useQueryState('sortOrder', parseAsInteger.withDefault(1));
  const [filters, setFilters] = useQueryState('filters', {
    defaultValue: {
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    },
    parse: value => {
      try {
        return JSON.parse(value) as FilterState;
      } catch {
        return {
          category: 'all',
          priceRange: '0-100',
          sortBy: 'name',
          inStock: true,
        };
      }
    },
    serialize: value => JSON.stringify(value),
  });

  // Update individual filter properties
  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters({
      ...filters,
      [key]: value,
    });
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm(null);
    setPage(1);
    setSortOrder(1);
    setFilters({
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-green-600 mb-2">Nuqs useQueryState</h1>
        <p className="text-muted-foreground mb-4">Clean, type-safe, and minimal boilerplate URL state management</p>
        <Link href="/traditional" className="text-blue-600 hover:underline">
          ← Compare with Traditional approach
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Search & Filters</CardTitle>
          <CardDescription>This page uses nuqs with automatic URL synchronization and type safety</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search */}
          <div>
            <label className="text-sm font-medium">Search Products:</label>
            <Input
              value={searchTerm || ''}
              onChange={e => setSearchTerm(e.target.value || null)}
              placeholder="Enter search term..."
              className="mt-1"
            />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Category:</label>
              <Select value={filters?.category} onValueChange={value => updateFilter('category', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="clothing">Clothing</SelectItem>
                  <SelectItem value="books">Books</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Price Range:</label>
              <Select value={filters?.priceRange} onValueChange={value => updateFilter('priceRange', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-100">$0 - $100</SelectItem>
                  <SelectItem value="100-500">$100 - $500</SelectItem>
                  <SelectItem value="500-1000">$500 - $1000</SelectItem>
                  <SelectItem value="1000+">$1000+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Sort By:</label>
              <Select value={filters?.sortBy} onValueChange={value => updateFilter('sortBy', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="date">Date Added</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">In Stock Only:</label>
              <div className="mt-1">
                <Button variant={filters?.inStock ? 'default' : 'outline'} size="sm" onClick={() => updateFilter('inStock', !filters?.inStock)}>
                  {filters?.inStock ? 'Yes' : 'No'}
                </Button>
              </div>
            </div>
          </div>

          {/* Pagination and Sort Order */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => setPage(Math.max(1, (page || 1) - 1))} disabled={(page || 1) <= 1}>
                  Previous
                </Button>
                <span className="text-sm">Page {page || 1}</span>
                <Button variant="outline" size="sm" onClick={() => setPage((page || 1) + 1)}>
                  Next
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">Sort Order:</span>
                <Button variant="outline" size="sm" onClick={() => setSortOrder((sortOrder || 1) === 1 ? 2 : 1)}>
                  {sortOrder === 1 ? 'Ascending' : 'Descending'}
                </Button>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Reset All
            </Button>
          </div>

          {/* Current state display */}
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Current State:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(
                {
                  search: searchTerm,
                  page: page,
                  sortOrder: sortOrder,
                  filters: filters,
                },
                null,
                2
              )}
            </pre>
          </div>

          {/* URL display */}
          <div className="text-xs text-muted-foreground">
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.search : ''}
          </div>

          {/* Code comparison */}
          <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-green-800 mb-2">Nuqs Approach Benefits:</h4>
            <ul className="text-xs text-green-700 space-y-1">
              <li>• Automatic URL synchronization - no manual parsing</li>
              <li>• Built-in type safety with TypeScript</li>
              <li>• Automatic type conversion (string → number, boolean, etc.)</li>
              <li>• Minimal boilerplate code</li>
              <li>• Automatic serialization/deserialization</li>
              <li>• Built-in error handling</li>
              <li>• Clean, declarative API</li>
              <li>• No useEffect needed for state sync</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
