import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">Nuqs Knowledge Sharing Session</h1>
        <p className="text-xl text-muted-foreground mb-6">
          Comparing traditional useSearchParams vs nuqs for URL state management
        </p>
        <div className="flex gap-4 justify-center">
          <Link href="/traditional">
            <Button variant="outline" size="lg">
              View Traditional Approach
            </Button>
          </Link>
          <Link href="/nuqs">
            <Button size="lg">
              View Nuqs Approach
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="comparison">Code Comparison</TabsTrigger>
          <TabsTrigger value="benefits">Benefits</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Traditional useSearchParams</CardTitle>
                <CardDescription>
                  Manual URL manipulation and state synchronization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Manual parsing of search parameters</li>
                  <li>• Complex useEffect for state synchronization</li>
                  <li>• No type safety</li>
                  <li>• Lots of boilerplate code</li>
                  <li>• Manual error handling</li>
                  <li>• Need to manage router separately</li>
                </ul>
                <Link href="/traditional">
                  <Button className="w-full mt-4" variant="outline">
                    Try Traditional Approach
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-green-600">Nuqs useQueryState</CardTitle>
                <CardDescription>
                  Clean, type-safe, and minimal boilerplate
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li>• Automatic URL synchronization</li>
                  <li>• Built-in TypeScript support</li>
                  <li>• Minimal boilerplate code</li>
                  <li>• Automatic serialization</li>
                  <li>• Built-in error handling</li>
                  <li>• Clean, declarative API</li>
                </ul>
                <Link href="/nuqs">
                  <Button className="w-full mt-4">
                    Try Nuqs Approach
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Code Comparison</CardTitle>
              <CardDescription>
                See the difference in implementation complexity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-red-600 mb-3">Traditional Approach</h3>
                  <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
{`// Manual state management
const [searchTerm, setSearchTerm] = useState('')
const [page, setPage] = useState(1)

// Manual URL updates
const updateURL = (updates) => {
  const params = new URLSearchParams(searchParams.toString())
  Object.entries(updates).forEach(([key, value]) => {
    if (value === null) {
      params.delete(key)
    } else {
      params.set(key, value)
    }
  })
  router.push(\`?\${params.toString()}\`)
}

// Complex useEffect for sync
useEffect(() => {
  setSearchTerm(searchParams.get('search') || '')
  setPage(parseInt(searchParams.get('page') || '1'))
}, [searchParams])`}
                  </pre>
                </div>

                <div>
                  <h3 className="font-semibold text-green-600 mb-3">Nuqs Approach</h3>
                  <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
{`// Clean, declarative state
const [searchTerm, setSearchTerm] = useQueryState('search')
const [page, setPage] = useQueryState('page', { 
  defaultValue: 1 
})

// Automatic URL sync - no manual code needed!
// Just use the state setters normally
setSearchTerm('new value')
setPage(2)`}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="benefits" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Key Benefits of Nuqs</CardTitle>
              <CardDescription>
                Why nuqs is superior to traditional approaches
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-3">Developer Experience</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• 70% less boilerplate code</li>
                    <li>• No manual URL parsing</li>
                    <li>• Automatic state synchronization</li>
                    <li>• Intuitive API design</li>
                    <li>• Better debugging experience</li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Type Safety & Performance</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• Full TypeScript support</li>
                    <li>• Runtime type validation</li>
                    <li>• Optimized re-renders</li>
                    <li>• Smaller bundle size</li>
                    <li>• Better error handling</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Perfect for:</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• E-commerce filtering and sorting</li>
                  <li>• Dashboard configurations</li>
                  <li>• Form state persistence</li>
                  <li>• Multi-step workflows</li>
                  <li>• Search and pagination</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="mt-8 text-center">
        <p className="text-muted-foreground mb-4">
          Ready to see the difference? Navigate to both pages and compare the implementation!
        </p>
        <div className="flex gap-4 justify-center">
          <Link href="/traditional">
            <Button variant="outline">
              Traditional useSearchParams
            </Button>
          </Link>
          <Link href="/nuqs">
            <Button>
              Nuqs useQueryState
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
} 