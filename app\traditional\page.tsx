'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import Link from 'next/link';

interface FilterState {
  category: string;
  priceRange: string;
  sortBy: string;
  inStock: boolean;
}

export default function TraditionalPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Manual parsing of search parameters
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [page, setPage] = useState(parseInt(searchParams.get('page') || '1'));
  const [sortOrder, setSortOrder] = useState(parseInt(searchParams.get('sortOrder') || '1'));
  const [filters, setFilters] = useState<FilterState>(() => {
    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        return JSON.parse(filtersParam);
      } catch {
        return {
          category: 'all',
          priceRange: '0-100',
          sortBy: 'name',
          inStock: true,
        };
      }
    }
    return {
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    };
  });

  // Update URL when state changes
  const updateURL = (updates: Record<string, string | null>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '') {
        params.delete(key);
      } else {
        params.set(key, value);
      }
    });

    router.push(`?${params.toString()}`);
  };

  // Handle search term changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateURL({ search: value || null });
  };

  // Handle page changes
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    updateURL({ page: newPage.toString() });
  };

  // Handle sort order changes
  const handleSortOrderChange = (newSortOrder: number) => {
    setSortOrder(newSortOrder);
    updateURL({ sortOrder: newSortOrder.toString() });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterState, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateURL({ filters: JSON.stringify(newFilters) });
  };

  // Reset all filters
  const resetFilters = () => {
    const defaultFilters = {
      category: 'all',
      priceRange: '0-100',
      sortBy: 'name',
      inStock: true,
    };
    setFilters(defaultFilters);
    setSearchTerm('');
    setPage(1);
    setSortOrder(1);
    updateURL({
      search: null,
      page: '1',
      sortOrder: '1',
      filters: JSON.stringify(defaultFilters),
    });
  };

  // Sync state with URL on mount and URL changes
  useEffect(() => {
    setSearchTerm(searchParams.get('search') || '');
    setPage(parseInt(searchParams.get('page') || '1'));
    setSortOrder(parseInt(searchParams.get('sortOrder') || '1'));

    const filtersParam = searchParams.get('filters');
    if (filtersParam) {
      try {
        setFilters(JSON.parse(filtersParam));
      } catch {
        setFilters({
          category: 'all',
          priceRange: '0-100',
          sortBy: 'name',
          inStock: true,
        });
      }
    }
  }, [searchParams]);

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-red-600 mb-2">Traditional useSearchParams</h1>
        <p className="text-muted-foreground mb-4">Manual URL manipulation, parsing, and state synchronization</p>
        <Link href="/nuqs" className="text-blue-600 hover:underline">
          ← Compare with Nuqs approach
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Product Search & Filters</CardTitle>
          <CardDescription>This page uses traditional useSearchParams with manual URL manipulation</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search */}
          <div>
            <label className="text-sm font-medium">Search Products:</label>
            <Input value={searchTerm} onChange={e => handleSearchChange(e.target.value)} placeholder="Enter search term..." className="mt-1" />
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Category:</label>
              <Select value={filters.category} onValueChange={value => handleFilterChange('category', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="electronics">Electronics</SelectItem>
                  <SelectItem value="clothing">Clothing</SelectItem>
                  <SelectItem value="books">Books</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Price Range:</label>
              <Select value={filters.priceRange} onValueChange={value => handleFilterChange('priceRange', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0-100">$0 - $100</SelectItem>
                  <SelectItem value="100-500">$100 - $500</SelectItem>
                  <SelectItem value="500-1000">$500 - $1000</SelectItem>
                  <SelectItem value="1000+">$1000+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Sort By:</label>
              <Select value={filters.sortBy} onValueChange={value => handleFilterChange('sortBy', value)}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="date">Date Added</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">In Stock Only:</label>
              <div className="mt-1">
                <Button variant={filters.inStock ? 'default' : 'outline'} size="sm" onClick={() => handleFilterChange('inStock', !filters.inStock)}>
                  {filters.inStock ? 'Yes' : 'No'}
                </Button>
              </div>
            </div>
          </div>

          {/* Pagination and Sort Order */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => handlePageChange(Math.max(1, page - 1))} disabled={page <= 1}>
                  Previous
                </Button>
                <span className="text-sm">Page {page}</span>
                <Button variant="outline" size="sm" onClick={() => handlePageChange(page + 1)}>
                  Next
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">Sort Order:</span>
                <Button variant="outline" size="sm" onClick={() => handleSortOrderChange(sortOrder === 1 ? 2 : 1)}>
                  {sortOrder === 1 ? 'Ascending' : 'Descending'}
                </Button>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={resetFilters}>
              Reset All
            </Button>
          </div>

          {/* Current state display */}
          <div className="bg-muted p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Current State:</h4>
            <pre className="text-xs overflow-auto">
              {JSON.stringify(
                {
                  search: searchTerm,
                  page: page,
                  sortOrder: sortOrder,
                  filters: filters,
                },
                null,
                2
              )}
            </pre>
          </div>

          {/* URL display */}
          <div className="text-xs text-muted-foreground">
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.search : ''}
          </div>

          {/* Code comparison */}
          <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-red-800 mb-2">Traditional Approach Challenges:</h4>
            <ul className="text-xs text-red-700 space-y-1">
              <li>• Manual URL parsing and serialization</li>
              <li>• Complex state synchronization with useEffect</li>
              <li>• No type safety for URL parameters</li>
              <li>• Manual type conversion (parseInt, JSON.parse)</li>
              <li>• Boilerplate code for every parameter</li>
              <li>• Manual error handling for JSON parsing</li>
              <li>• Need to manage router and searchParams separately</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
