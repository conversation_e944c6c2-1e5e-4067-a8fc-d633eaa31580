'use client';

import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

export function DemoBasic() {
  // Traditional useSearchParams approach
  const searchParams = useSearchParams();
  const traditionalQuery = searchParams.get('traditional-query') || '';
  const traditionalPage = parseInt(searchParams.get('traditional-page') || '1');

  // Nuqs approach
  const [nuqsQuery, setNuqsQuery] = useQueryState('nuqs-query');
  const [nuqsPage, setNuqsPage] = useQueryState('nuqs-page', { defaultValue: 1 });

  const handleTraditionalQueryChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value) {
      params.set('traditional-query', value);
    } else {
      params.delete('traditional-query');
    }
    window.history.pushState(null, '', `?${params.toString()}`);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  const handleTraditionalPageChange = (page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('traditional-page', page.toString());
    window.history.pushState(null, '', `?${params.toString()}`);
    window.dispatchEvent(new PopStateEvent('popstate'));
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Traditional useSearchParams */}
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Traditional useSearchParams</CardTitle>
          <CardDescription>Manual parsing, no type safety, lots of boilerplate</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Search Query:</label>
            <Input
              value={traditionalQuery}
              onChange={e => handleTraditionalQueryChange(e.target.value)}
              placeholder="Enter search term..."
              className="mt-1"
            />
          </div>
          <div>
            <label className="text-sm font-medium">Page:</label>
            <div className="flex gap-2 mt-1">
              <Button variant="outline" size="sm" onClick={() => handleTraditionalPageChange(Math.max(1, traditionalPage - 1))}>
                Previous
              </Button>
              <span className="flex items-center px-3 text-sm">{traditionalPage}</span>
              <Button variant="outline" size="sm" onClick={() => handleTraditionalPageChange(traditionalPage + 1)}>
                Next
              </Button>
            </div>
          </div>
          <div className="text-xs text-muted-foreground">
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.search : ''}
          </div>
        </CardContent>
      </Card>

      {/* Nuqs approach */}
      <Card>
        <CardHeader>
          <CardTitle className="text-green-600">Nuqs useQueryState</CardTitle>
          <CardDescription>Type-safe, minimal boilerplate, automatic serialization</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium">Search Query:</label>
            <Input value={nuqsQuery || ''} onChange={e => setNuqsQuery(e.target.value || null)} placeholder="Enter search term..." className="mt-1" />
          </div>
          <div>
            <label className="text-sm font-medium">Page:</label>
            <div className="flex gap-2 mt-1">
              <Button variant="outline" size="sm" onClick={() => setNuqsPage(Math.max(1, (nuqsPage || 1) - 1))}>
                Previous
              </Button>
              <span className="flex items-center px-3 text-sm">{nuqsPage || 1}</span>
              <Button variant="outline" size="sm" onClick={() => setNuqsPage((nuqsPage || 1) + 1)}>
                Next
              </Button>
            </div>
          </div>
          <div className="text-xs text-muted-foreground">
            <strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.search : ''}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
